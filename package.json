{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^3.12.1", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tsparticles/react": "^3.0.0", "@vercel/analytics": "^1.2.2", "@vercel/speed-insights": "^1.0.10", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.2.5", "lucide-react": "^0.379.0", "next": "^14.2.5", "next-themes": "^0.2.1", "portfolio": "file:", "react": "^18", "react-dom": "^18", "react-lazy-load-image-component": "^1.6.0", "react-tilt": "^1.0.2", "react-tsparticles": "^2.12.2", "sonner": "^1.3.1", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "typewriter-effect": "^2.21.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-lazy-load-image-component": "^1.6.4", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8", "eslint-config-next": "14.2.3", "eslint-import-resolver-typescript": "^4.4.2", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}